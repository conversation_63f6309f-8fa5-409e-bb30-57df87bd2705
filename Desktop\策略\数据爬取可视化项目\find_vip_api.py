#!/usr/bin/env python3
"""
寻找东方财富VIP人气榜的真实API
通过模拟浏览器请求来获取真实数据
"""
import requests
import json
import time
import re

def find_vip_popularity_api():
    """寻找VIP人气榜的真实API"""
    
    # 模拟真实浏览器的请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html',
        'Origin': 'https://vipmoney.eastmoney.com',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
    }
    
    # 可能的真实API端点
    possible_apis = [
        {
            'name': 'VIP人气榜API - 直接访问',
            'url': 'https://vipmoney.eastmoney.com/api/ranking/popularity',
            'method': 'GET'
        },
        {
            'name': 'VIP人气榜API - 带参数',
            'url': 'https://vipmoney.eastmoney.com/api/ranking/popularity?page=1&size=100',
            'method': 'GET'
        },
        {
            'name': 'VIP人气榜API - POST请求',
            'url': 'https://vipmoney.eastmoney.com/api/ranking/popularity',
            'method': 'POST',
            'data': {'page': 1, 'size': 100, 'type': 'popularity'}
        },
        {
            'name': 'VIP人气榜API - 股票列表',
            'url': 'https://vipmoney.eastmoney.com/api/ranking/stock/popularity/list',
            'method': 'GET'
        },
        {
            'name': 'VIP人气榜API - 数据接口',
            'url': 'https://vipmoney.eastmoney.com/api/data/popularity/ranking',
            'method': 'GET'
        },
        {
            'name': 'VIP人气榜API - 榜单数据',
            'url': 'https://vipmoney.eastmoney.com/api/ranking/list',
            'method': 'POST',
            'data': {'type': 'popularity', 'page': 1, 'size': 100}
        },
        {
            'name': 'VIP人气榜API - 实时数据',
            'url': 'https://vipmoney.eastmoney.com/api/realtime/popularity',
            'method': 'GET'
        }
    ]
    
    print("寻找东方财富VIP人气榜真实API...")
    
    for api in possible_apis:
        print(f"\n{'='*70}")
        print(f"测试: {api['name']}")
        print(f"URL: {api['url']}")
        print(f"方法: {api['method']}")
        
        try:
            if api['method'] == 'GET':
                response = requests.get(api['url'], headers=headers, timeout=15)
            else:
                response = requests.post(api['url'], headers=headers, json=api.get('data', {}), timeout=15)
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                text = response.text
                print(f"响应长度: {len(text)}")
                print(f"响应前500字符: {text[:500]}")
                
                # 尝试解析JSON
                try:
                    data = json.loads(text)
                    print("✅ JSON解析成功")
                    
                    # 分析数据结构
                    if 'data' in data:
                        if isinstance(data['data'], list):
                            stocks = data['data']
                            print(f"股票数量: {len(stocks)}")
                            if stocks:
                                print("前3只股票:")
                                for i, stock in enumerate(stocks[:3]):
                                    if isinstance(stock, dict):
                                        name = stock.get('name', stock.get('stockName', stock.get('title', 'N/A')))
                                        code = stock.get('code', stock.get('stockCode', stock.get('symbol', 'N/A')))
                                        price = stock.get('price', stock.get('latestPrice', stock.get('currentPrice', 0)))
                                        change_pct = stock.get('changePct', stock.get('changePercent', stock.get('changeRate', 0)))
                                        popularity = stock.get('popularity', stock.get('popularityValue', stock.get('heat', 0)))
                                        print(f"  {i+1}. {name} ({code}) - 价格: {price} 涨跌幅: {change_pct}% 人气值: {popularity}")
                        elif isinstance(data['data'], dict):
                            print("数据结构:", list(data['data'].keys()))
                            if 'list' in data['data']:
                                stocks = data['data']['list']
                                print(f"股票数量: {len(stocks)}")
                                if stocks:
                                    print("前3只股票:")
                                    for i, stock in enumerate(stocks[:3]):
                                        if isinstance(stock, dict):
                                            name = stock.get('name', stock.get('stockName', 'N/A'))
                                            code = stock.get('code', stock.get('stockCode', 'N/A'))
                                            price = stock.get('price', stock.get('latestPrice', 0))
                                            change_pct = stock.get('changePct', stock.get('changePercent', 0))
                                            print(f"  {i+1}. {name} ({code}) - 价格: {price} 涨跌幅: {change_pct}%")
                    
                    elif 'result' in data:
                        result = data['result']
                        if isinstance(result, list):
                            print(f"结果数量: {len(result)}")
                            if result:
                                print("前3条结果:")
                                for i, item in enumerate(result[:3]):
                                    if isinstance(item, dict):
                                        name = item.get('name', item.get('stockName', 'N/A'))
                                        code = item.get('code', item.get('stockCode', 'N/A'))
                                        print(f"  {i+1}. {name} ({code})")
                        elif isinstance(result, dict):
                            print("结果结构:", list(result.keys()))
                    
                    elif 'list' in data:
                        stocks = data['list']
                        print(f"股票数量: {len(stocks)}")
                        if stocks:
                            print("前3只股票:")
                            for i, stock in enumerate(stocks[:3]):
                                if isinstance(stock, dict):
                                    name = stock.get('name', stock.get('stockName', 'N/A'))
                                    code = stock.get('code', stock.get('stockCode', 'N/A'))
                                    price = stock.get('price', stock.get('latestPrice', 0))
                                    change_pct = stock.get('changePct', stock.get('changePercent', 0))
                                    print(f"  {i+1}. {name} ({code}) - 价格: {price} 涨跌幅: {change_pct}%")
                    
                    else:
                        print("❌ 未知数据结构")
                        print(f"数据键: {list(data.keys())}")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    # 尝试查找HTML中的JSON数据
                    if 'script' in text.lower() or 'json' in text.lower():
                        print("可能包含JSON数据的HTML页面")
                    
            elif response.status_code == 404:
                print("❌ 404 - 接口不存在")
            elif response.status_code == 403:
                print("❌ 403 - 访问被拒绝")
            elif response.status_code == 401:
                print("❌ 401 - 需要认证")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"响应内容: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        time.sleep(2)  # 避免请求过于频繁

if __name__ == "__main__":
    find_vip_popularity_api() 