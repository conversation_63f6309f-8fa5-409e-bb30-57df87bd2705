#!/usr/bin/env python3
"""
提取东方财富VIP页面的完整股票数据
"""
import re
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import time

def extract_vip_stocks():
    """提取VIP页面的股票数据"""
    
    url = "https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/stock"
    
    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    print("正在提取东方财富VIP页面股票数据...")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        driver.get(url)
        print("页面加载中...")
        
        # 等待页面完全加载
        time.sleep(20)
        
        # 获取页面文本内容
        body_text = driver.find_element(By.TAG_NAME, "body").text
        print(f"页面文本长度: {len(body_text)}")
        
        # 使用正则表达式提取股票信息
        # 模式：股票名称 + 股票代码 + 价格 + 涨跌幅
        stock_pattern = r'([^\d\n]+?)\s*(\d{6})\s*融?\s*(\d+\.?\d*)\s*([+-]?\d+\.?\d*)%'
        
        matches = re.findall(stock_pattern, body_text)
        print(f"找到股票匹配: {len(matches)} 个")
        
        stocks = []
        for i, match in enumerate(matches, 1):
            name = match[0].strip()
            code = match[1]
            price = float(match[2])
            change_pct = float(match[3])
            
            # 过滤掉明显不是股票的数据
            if len(name) > 1 and len(name) < 10 and not name.isdigit():
                stock_info = {
                    'rank': i,
                    'name': name,
                    'code': code,
                    'price': price,
                    'change_pct': change_pct,
                    'change_amount': 0,  # 暂时设为0
                    'volume': 0,  # 暂时设为0
                    'amount': 0,  # 暂时设为0
                    'turnover': 0,  # 暂时设为0
                    'popularity': i  # 使用排名作为人气值
                }
                stocks.append(stock_info)
                print(f"{i}. {name} ({code}) - 价格: {price} 涨跌幅: {change_pct}%")
        
        # 如果正则表达式没有找到足够的数据，尝试其他方法
        if len(stocks) < 10:
            print("\n尝试其他提取方法...")
            
            # 按行分割文本
            lines = body_text.split('\n')
            stock_lines = []
            
            for line in lines:
                line = line.strip()
                if line and len(line) > 0:
                    # 查找包含6位数字的行
                    if re.search(r'\d{6}', line):
                        # 查找包含百分比的行
                        if '%' in line:
                            stock_lines.append(line)
            
            print(f"找到包含股票信息的行: {len(stock_lines)} 行")
            
            # 显示前20行
            for i, line in enumerate(stock_lines[:20]):
                print(f"行 {i+1}: {line}")
        
        driver.quit()
        
        return stocks
        
    except Exception as e:
        print(f"提取失败: {e}")
        if 'driver' in locals():
            driver.quit()
        return []

if __name__ == "__main__":
    stocks = extract_vip_stocks()
    print(f"\n=== 提取结果 ===")
    print(f"总共提取到 {len(stocks)} 只股票")
    
    if stocks:
        print("\n前10只股票:")
        for i, stock in enumerate(stocks[:10]):
            print(f"{stock['rank']}. {stock['name']} ({stock['code']}) - 价格: {stock['price']} 涨跌幅: {stock['change_pct']}%")
        
        # 保存为JSON文件
        with open('vip_stocks.json', 'w', encoding='utf-8') as f:
            json.dump(stocks, f, ensure_ascii=False, indent=2)
        print(f"\n数据已保存到 vip_stocks.json") 