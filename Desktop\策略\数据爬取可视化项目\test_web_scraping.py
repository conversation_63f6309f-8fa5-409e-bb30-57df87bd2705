"""
测试从东方财富网页直接爬取数据
"""
import requests
from bs4 import BeautifulSoup
import json
import re
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def test_direct_web_scraping():
    """测试直接网页爬取"""
    print("=" * 60)
    print("测试从东方财富网页直接爬取数据")
    print("=" * 60)
    
    url = "https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/stock"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    print(f"1. 尝试直接请求页面: {url}")
    
    try:
        session = requests.Session()
        session.headers.update(headers)
        
        response = session.get(url, timeout=15)
        print(f"状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)}")
        
        if response.status_code == 200:
            content = response.text
            print(f"响应前500字符: {content[:500]}")
            
            # 检查是否是SPA应用
            if 'app.html' in content or 'vue' in content.lower() or 'react' in content.lower():
                print("⚠️ 这是一个单页应用(SPA)，需要使用Selenium")
                return False
            
            # 尝试解析HTML
            soup = BeautifulSoup(content, 'html.parser')
            
            # 查找可能的股票数据
            stock_elements = soup.find_all(['div', 'tr', 'li'], class_=re.compile(r'stock|rank|item', re.I))
            print(f"找到可能的股票元素: {len(stock_elements)}个")
            
            # 查找包含股票代码的元素
            code_pattern = re.compile(r'\d{6}')
            found_stocks = []
            
            for elem in stock_elements[:20]:
                text = elem.get_text(strip=True)
                if code_pattern.search(text) and len(text) > 10:
                    found_stocks.append(text[:100])
            
            if found_stocks:
                print("✅ 找到可能的股票数据:")
                for i, stock in enumerate(found_stocks[:5], 1):
                    print(f"  {i}. {stock}")
                return True
            else:
                print("❌ 未找到股票数据")
                return False
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_selenium_scraping():
    """使用Selenium爬取动态页面"""
    print("\n" + "=" * 60)
    print("使用Selenium爬取动态页面")
    print("=" * 60)
    
    url = "https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/stock"
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    try:
        print("启动Chrome浏览器...")
        driver = webdriver.Chrome(options=chrome_options)
        
        print(f"访问页面: {url}")
        driver.get(url)
        
        # 等待页面加载
        print("等待页面加载...")
        time.sleep(5)
        
        # 等待股票列表加载
        try:
            # 尝试等待包含股票数据的元素
            wait = WebDriverWait(driver, 10)
            
            # 可能的选择器
            selectors = [
                '[class*="stock"]',
                '[class*="rank"]',
                '[class*="list"]',
                'table tbody tr',
                '.ranking-list',
                '.stock-item',
                '[data-code]'
            ]
            
            found_element = None
            for selector in selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        print(f"✅ 找到元素: {selector} ({len(elements)}个)")
                        found_element = elements
                        break
                except:
                    continue
            
            if found_element:
                print("解析股票数据...")
                stocks = []
                
                for i, elem in enumerate(found_element[:10]):
                    try:
                        text = elem.text.strip()
                        if text and len(text) > 10:
                            # 检查是否包含股票代码
                            if re.search(r'\d{6}', text):
                                stocks.append(text)
                                print(f"  {i+1}. {text[:100]}")
                    except:
                        continue
                
                if stocks:
                    print(f"✅ 成功获取到 {len(stocks)} 只股票数据")
                    return stocks
                else:
                    print("❌ 未找到有效的股票数据")
            
            else:
                print("❌ 未找到股票列表元素")
                
                # 打印页面源码的一部分用于调试
                page_source = driver.page_source
                print(f"页面源码长度: {len(page_source)}")
                print(f"页面源码片段: {page_source[:1000]}")
                
                # 查找所有可能包含数据的元素
                all_elements = driver.find_elements(By.CSS_SELECTOR, '*')
                print(f"页面总元素数: {len(all_elements)}")
                
                # 查找包含数字的元素
                for elem in all_elements[:50]:
                    try:
                        text = elem.text.strip()
                        if text and re.search(r'\d{6}', text):
                            print(f"可能的股票元素: {text[:100]}")
                    except:
                        continue
        
        except Exception as e:
            print(f"❌ 等待元素失败: {e}")
        
        finally:
            driver.quit()
            
    except Exception as e:
        print(f"❌ Selenium失败: {e}")
        print("请确保已安装Chrome浏览器和ChromeDriver")
        return None

def test_alternative_urls():
    """测试其他可能的URL"""
    print("\n" + "=" * 60)
    print("测试其他可能的URL")
    print("=" * 60)
    
    # 可能的URL列表
    urls = [
        "https://guba.eastmoney.com/rank/stock",
        "https://data.eastmoney.com/rank/stock.html",
        "https://quote.eastmoney.com/rank/stock.html",
        "https://datacenter.eastmoney.com/securities/api/data/get",
        "https://push2.eastmoney.com/api/qt/clist/get?cb=jQuery&pn=1&pz=50&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&fid=f3&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25"
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://www.eastmoney.com/',
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    for i, url in enumerate(urls, 1):
        print(f"\n{i}. 测试: {url[:60]}...")
        
        try:
            response = session.get(url, timeout=10)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                
                # 检查是否包含目标股票
                target_stocks = ['长城军工', '601606', '上纬新材', '688585']
                found_targets = [stock for stock in target_stocks if stock in content]
                
                if found_targets:
                    print(f"🎯 找到目标股票: {found_targets}")
                    print(f"响应长度: {len(content)}")
                    
                    # 如果是API响应，尝试解析
                    if content.strip().startswith('jQuery') or content.strip().startswith('{'):
                        print("✅ 这是一个有效的API接口")
                        return url
                    else:
                        print("✅ 这是一个包含目标数据的网页")
                        return url
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
        
        time.sleep(1)  # 避免请求过快
    
    return None

if __name__ == "__main__":
    # 1. 测试直接网页爬取
    success = test_direct_web_scraping()
    
    # 2. 如果直接爬取失败，尝试Selenium
    if not success:
        test_selenium_scraping()
    
    # 3. 测试其他URL
    best_url = test_alternative_urls()
    
    if best_url:
        print(f"\n🎯 推荐使用的URL: {best_url}")
    else:
        print("\n❌ 未找到合适的数据源")
