"""
测试网页爬虫功能
"""
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.web_scraper import EastMoneyWebScraper

def test_web_scraper():
    """测试网页爬虫"""
    print("=" * 60)
    print("测试东方财富网页爬虫")
    print("=" * 60)
    
    scraper = EastMoneyWebScraper()
    
    try:
        print("开始爬取热股榜数据...")
        data = scraper.fetch_hotstock_from_web()
        
        if data:
            print(f"✅ 成功获取到 {len(data)} 只股票数据")
            
            # 查找目标股票
            target_stocks = ['长城军工', '601606', '上纬新材', '688585']
            found_targets = []
            
            print("\n前10只股票:")
            for i, stock in enumerate(data[:10], 1):
                name = stock.get('name', '')
                code = stock.get('code', '')
                price = stock.get('price', 0)
                change_pct = stock.get('change_pct', 0)
                popularity = stock.get('popularity', 0)
                
                print(f"{i:2d}. {name} ({code}) {price} {change_pct:+.2f}% 人气:{popularity}")
                
                # 检查是否是目标股票
                if name in target_stocks or code in target_stocks:
                    found_targets.append(f"{name} ({code})")
            
            if found_targets:
                print(f"\n🎯 找到目标股票: {', '.join(found_targets)}")
            else:
                print("\n❌ 未找到目标股票")
                
                # 搜索所有数据中的目标股票
                print("\n在所有数据中搜索目标股票...")
                for stock in data:
                    name = stock.get('name', '')
                    code = stock.get('code', '')
                    if name in target_stocks or code in target_stocks:
                        price = stock.get('price', 0)
                        change_pct = stock.get('change_pct', 0)
                        rank = stock.get('rank', 0)
                        print(f"🎯 第{rank}名: {name} ({code}) {price} {change_pct:+.2f}%")
            
            print(f"\n数据统计:")
            print(f"总股票数: {len(data)}")
            
            # 统计涨跌情况
            rise_count = sum(1 for stock in data if stock.get('change_pct', 0) > 0)
            fall_count = sum(1 for stock in data if stock.get('change_pct', 0) < 0)
            equal_count = len(data) - rise_count - fall_count
            
            print(f"上涨股票: {rise_count}只")
            print(f"下跌股票: {fall_count}只")
            print(f"平盘股票: {equal_count}只")
            
            # 统计价格范围
            prices = [stock.get('price', 0) for stock in data if stock.get('price', 0) > 0]
            if prices:
                print(f"价格范围: {min(prices):.2f} - {max(prices):.2f}")
            
        else:
            print("❌ 未获取到数据")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 清理资源
        scraper.close()
        print("\n测试完成")

if __name__ == "__main__":
    test_web_scraper()
