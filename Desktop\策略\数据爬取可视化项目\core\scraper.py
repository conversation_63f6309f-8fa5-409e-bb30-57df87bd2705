"""
东方财富数据爬虫
"""
import requests
import json
import re
import time
import logging
from typing import List, Dict, Optional
from PyQt5.QtCore import QObject, pyqtSignal

from .config import HEADERS, EASTMONEY_URLS, RETRY_COUNT, RETRY_DELAY
from .web_scraper import EastMoneyWebScraper

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EastMoneyScraper(QObject):
    """东方财富数据爬虫"""
    
    # 信号定义
    data_updated = pyqtSignal(str, list)  # 数据类型, 数据列表
    error_occurred = pyqtSignal(str)      # 错误信息
    status_changed = pyqtSignal(str)      # 状态信息
    
    def __init__(self):
        super().__init__()
        self.session = requests.Session()
        self.session.headers.update(HEADERS)
        self.web_scraper = None  # 网页爬虫实例
    
    def _make_request(self, url: str) -> Optional[str]:
        """发送HTTP请求"""
        for attempt in range(RETRY_COUNT):
            try:
                self.status_changed.emit(f"正在请求数据... (尝试 {attempt + 1}/{RETRY_COUNT})")
                
                response = self.session.get(url, timeout=10)
                response.raise_for_status()
                
                return response.text
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"请求失败 (尝试 {attempt + 1}): {e}")
                if attempt < RETRY_COUNT - 1:
                    time.sleep(RETRY_DELAY)
                else:
                    self.error_occurred.emit(f"网络请求失败: {str(e)}")
                    
        return None
    
    def _parse_jsonp_response(self, text: str) -> Optional[Dict]:
        """解析JSONP响应"""
        try:
            # 移除JSONP回调函数包装
            if text.startswith('jQuery'):
                start = text.find('(') + 1
                end = text.rfind(')')
                text = text[start:end]
            
            return json.loads(text)
            
        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"JSON解析失败: {e}")
            self.error_occurred.emit(f"数据解析失败: {str(e)}")
            return None
    
    def _parse_stock_data(self, raw_data: Dict, data_type: str) -> List[Dict]:
        """解析股票数据"""
        try:
            if not raw_data or 'data' not in raw_data:
                return []
            
            data = raw_data['data']
            if not data or 'diff' not in data:
                return []
            
            stocks = data['diff']
            parsed_stocks = []
            
            for i, stock in enumerate(stocks, 1):
                try:
                    # 解析股票信息 - 支持两种API格式
                    if data_type == 'popularity' and 'f148' in stock:
                        # VIP人气榜API格式
                        stock_info = {
                            'rank': i,
                            'code': str(stock.get('f12', '')),  # 股票代码
                            'name': str(stock.get('f14', '')),  # 股票名称
                            'price': float(stock.get('f2', 0)) if stock.get('f2') else 0,   # 最新价
                            'change_pct': float(stock.get('f3', 0)) if stock.get('f3') else 0,  # 涨跌幅
                            'change_amount': float(stock.get('f4', 0)) if stock.get('f4') else 0,  # 涨跌额
                            'volume': int(stock.get('f5', 0)) if stock.get('f5') else 0,  # 成交量(手)
                            'amount': float(stock.get('f6', 0)) if stock.get('f6') else 0,  # 成交额(元)
                            'turnover': float(stock.get('f8', 0)) if stock.get('f8') else 0,  # 换手率
                            'popularity': int(stock.get('f148', 0)) if stock.get('f148') else 0  # 人气值
                        }
                    else:
                        # 原有API格式
                        stock_info = {
                            'rank': i,
                            'code': str(stock.get('f12', '')),  # 股票代码
                            'name': str(stock.get('f14', '')),  # 股票名称
                            'price': float(stock.get('f2', 0)) if stock.get('f2') else 0,   # 最新价
                            'change_pct': float(stock.get('f3', 0)) if stock.get('f3') else 0,  # 涨跌幅
                            'change_amount': float(stock.get('f4', 0)) if stock.get('f4') else 0,  # 涨跌额
                            'volume': int(stock.get('f5', 0)) if stock.get('f5') else 0,  # 成交量(手)
                            'amount': float(stock.get('f6', 0)) if stock.get('f6') else 0,  # 成交额(元)
                            'turnover': float(stock.get('f8', 0)) if stock.get('f8') else 0,  # 换手率
                            'popularity': int(stock.get('f164', 0)) if data_type == 'popularity' and stock.get('f164') else 0  # 人气值
                        }
                    
                    # 数据验证和清理
                    if not stock_info['code'] or not stock_info['name']:
                        continue  # 跳过无效数据
                    
                    parsed_stocks.append(stock_info)
                    
                except (KeyError, ValueError, TypeError) as e:
                    logger.warning(f"解析第{i}条股票数据失败: {e}")
                    continue
            
            return parsed_stocks
            
        except Exception as e:
            logger.error(f"解析股票数据失败: {e}")
            self.error_occurred.emit(f"数据解析失败: {str(e)}")
            return []
    
    def fetch_popularity_ranking(self) -> List[Dict]:
        """获取人气榜数据"""
        self.status_changed.emit("正在获取人气榜数据...")
        
        # 尝试主要API
        url = EASTMONEY_URLS['popularity']
        response_text = self._make_request(url)
        
        if response_text:
            raw_data = self._parse_jsonp_response(response_text)
            if raw_data:
                stocks = self._parse_stock_data(raw_data, 'popularity')
                if stocks:
                    self.status_changed.emit(f"成功获取人气榜数据: {len(stocks)}只股票")
                    self.data_updated.emit('popularity', stocks)
                    return stocks
        
        # 尝试备用API
        self.status_changed.emit("尝试备用数据源...")
        backup_url = EASTMONEY_URLS['backup_popularity']
        response_text = self._make_request(backup_url)
        
        if response_text:
            try:
                raw_data = json.loads(response_text)
                # 这里需要根据备用API的实际响应格式进行解析
                # 暂时返回空列表
                self.status_changed.emit("备用数据源暂不可用")
            except:
                pass
        
        self.error_occurred.emit("无法获取人气榜数据")
        return []
    
    def fetch_soaring_ranking(self) -> List[Dict]:
        """获取飙升榜数据"""
        self.status_changed.emit("正在获取飙升榜数据...")
        
        # 首先尝试网页爬取
        web_data = self._fetch_from_web()
        if web_data:
            self.status_changed.emit(f"网页爬取成功，获取到 {len(web_data)} 只股票")
            self.data_updated.emit('soaring', web_data)
            return web_data
        
        # 尝试主要API
        url = EASTMONEY_URLS['soaring']
        response_text = self._make_request(url)
        
        if response_text:
            raw_data = self._parse_jsonp_response(response_text)
            if raw_data:
                stocks = self._parse_stock_data(raw_data, 'soaring')
                if stocks:
                    self.status_changed.emit(f"成功获取飙升榜数据: {len(stocks)}只股票")
                    self.data_updated.emit('soaring', stocks)
                    return stocks
        
        # 尝试备用API
        self.status_changed.emit("尝试备用数据源...")
        backup_url = EASTMONEY_URLS['backup_soaring']
        response_text = self._make_request(backup_url)
        
        if response_text:
            try:
                raw_data = json.loads(response_text)
                # 这里需要根据备用API的实际响应格式进行解析
                # 暂时返回空列表
                self.status_changed.emit("备用数据源暂不可用")
            except:
                pass
        
        self.error_occurred.emit("无法获取飙升榜数据")
        return []

    def _fetch_from_web(self) -> List[Dict]:
        """从网页爬取数据"""
        try:
            import subprocess
            import json
            import os
            
            # 运行网页爬虫脚本
            script_path = os.path.join(os.path.dirname(__file__), '..', 'extract_vip_stocks.py')
            if os.path.exists(script_path):
                result = subprocess.run(['python', script_path], 
                                      capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    # 读取生成的JSON文件
                    json_path = os.path.join(os.path.dirname(__file__), '..', 'vip_stocks.json')
                    if os.path.exists(json_path):
                        with open(json_path, 'r', encoding='utf-8') as f:
                            stocks = json.load(f)
                        
                        # 转换为标准格式
                        formatted_stocks = []
                        for stock in stocks:
                            formatted_stock = {
                                'rank': stock['rank'],
                                'code': stock['code'],
                                'name': stock['name'],
                                'price': stock['price'],
                                'change_pct': stock['change_pct'],
                                'change_amount': stock.get('change_amount', 0),
                                'volume': stock.get('volume', 0),
                                'amount': stock.get('amount', 0),
                                'turnover': stock.get('turnover', 0),
                                'popularity': stock.get('popularity', stock['rank'])
                            }
                            formatted_stocks.append(formatted_stock)
                        
                        logger.info(f"从网页爬取到 {len(formatted_stocks)} 只股票")
                        return formatted_stocks
            
            return []
            
        except Exception as e:
            logger.error(f"网页爬取失败: {e}")
            return []

    def fetch_hotstock_from_web(self) -> List[Dict]:
        """从网页直接爬取热股榜数据"""
        self.status_changed.emit("正在从网页爬取热股榜数据...")

        try:
            # 初始化网页爬虫
            if self.web_scraper is None:
                self.web_scraper = EastMoneyWebScraper()

            # 从网页获取数据
            data = self.web_scraper.fetch_hotstock_from_web()

            if data:
                self.status_changed.emit(f"网页爬取成功，获取到 {len(data)} 只股票")
                self.data_updated.emit('hotstock', data)
                return data
            else:
                self.error_occurred.emit("网页爬取失败，未获取到数据")
                return []

        except Exception as e:
            error_msg = f"网页爬取失败: {str(e)}"
            self.error_occurred.emit(error_msg)
            return []

    def fetch_all_data(self):
        """获取所有数据"""
        self.status_changed.emit("开始更新数据...")
        
        # 获取人气榜
        popularity_data = self.fetch_popularity_ranking()
        
        # 短暂延迟避免请求过于频繁
        time.sleep(1)
        
        # 获取飙升榜
        soaring_data = self.fetch_soaring_ranking()
        
        if popularity_data or soaring_data:
            self.status_changed.emit("数据更新完成")
        else:
            self.status_changed.emit("数据更新失败")
    
    def format_number(self, num: float, unit: str = '') -> str:
        """格式化数字显示"""
        if num == 0:
            return '0'
        
        if abs(num) >= 100000000:  # 亿
            return f"{num/100000000:.2f}亿{unit}"
        elif abs(num) >= 10000:    # 万
            return f"{num/10000:.2f}万{unit}"
        else:
            return f"{num:.2f}{unit}"
