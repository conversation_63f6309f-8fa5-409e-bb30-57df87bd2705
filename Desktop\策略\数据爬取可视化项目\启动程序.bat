@echo off
chcp 65001 >nul
title 东方财富实时榜单监控

echo ========================================
echo 东方财富实时榜单监控 v1.0
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 切换到项目目录
cd /d "%~dp0"

:: 检查依赖是否安装
echo 📦 检查依赖包...
pip show PyQt5 >nul 2>&1
if errorlevel 1 (
    echo 📥 安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
)

:: 启动程序
echo 🚀 启动程序...
python main.py

if errorlevel 1 (
    echo ❌ 程序启动失败
    pause
)
