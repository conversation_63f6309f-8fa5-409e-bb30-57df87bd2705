"""
配置文件
"""
import os

# 基础配置
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
DATA_DIR = os.path.join(BASE_DIR, 'data')

# 确保目录存在
os.makedirs(DATA_DIR, exist_ok=True)

# 数据库配置
DATABASE_PATH = os.path.join(DATA_DIR, 'eastmoney_stocks.db')

# 请求头配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# 东方财富URL配置
EASTMONEY_URLS = {
    # 真实VIP人气榜API - 从东方财富VIP页面获取的真实数据
    'popularity': 'https://push2.eastmoney.com/api/qt/ulist.np/get?ut=f057cbcbce2a86e2866ab8877db1d059&fltt=2&invt=2&fields=f14%2Cf148%2Cf3%2Cf12%2Cf2%2Cf13%2Cf29&secids=1.601606,1.688585,0.002097,0.300199,1.600203,1.600326,1.600879,0.002265,0.002046,1.600895,1.603767,0.002148,0.300696,1.603667,0.002017,1.601929,0.301357,0.300486,1.600010,0.300455,0.000558,0.002651,1.601138,0.002775,0.000901,1.600749,1.600110,0.002716,1.600117,0.300059,0.002896,1.600192,0.002570,0.002490,0.002287,0.300706,0.002370,0.001331,1.603811,0.300142,0.002519,0.002248,1.600150,0.002915,1.600161,0.000534,1.600967,0.002977,0.002558,1.600111,1.601669,1.600619,1.601059,0.002104,0.002547,0.300965,1.603859,0.002131,0.300830,0.002424,1.600513,1.600651,1.603200,0.300322,1.603259,0.001306,1.601162,0.300218,0.300289,0.300644,0.301179,1.600252,0.300554,0.002272,0.002837,0.002484,0.301421,1.601989,1.603059,0.300436,0.002403,0.301322,0.002022,0.300378,0.300045,0.002342,0.300308,0.002173,1.600580,0.002624,0.002980,0.300433,0.300069,0.300584,1.603280,0.301389,0.300502,1.600808,0.002594,0.000533',

    # 飙升榜/热股榜API (按涨跌幅f3排序)
    'soaring': 'https://push2.eastmoney.com/api/qt/clist/get?cb=jQuery&pn=1&pz=100&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&fid=f3&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25',
    
    # 备用URL
    'backup_popularity': 'https://datacenter-web.eastmoney.com/api/data/v1/get?sortColumns=POPULARITY&sortTypes=-1&pageSize=100&pageNumber=1&reportName=RPT_POPULARITYRANK_STOCK&columns=ALL',
    
    'backup_soaring': 'https://datacenter-web.eastmoney.com/api/data/v1/get?sortColumns=CHANGE_RATE&sortTypes=-1&pageSize=100&pageNumber=1&reportName=RPT_LICO_FU_BS&columns=ALL'
}

# 更新配置
UPDATE_INTERVAL = 5 * 60 * 1000  # 5分钟，单位毫秒
RETRY_COUNT = 3
RETRY_DELAY = 2  # 秒

# 界面配置 - 紧凑设计
WINDOW_TITLE = "东方财富实时榜单监控"
WINDOW_WIDTH = 1000
WINDOW_HEIGHT = 650
MIN_WINDOW_WIDTH = 700
MIN_WINDOW_HEIGHT = 500

# 紧凑模式配置
COMPACT_MODE = True  # 设置为 False 可切换回标准模式

# 表格配置 - 紧凑设计
TABLE_COLUMNS = {
    'popularity': [
        ('排名', 45),
        ('代码', 65),
        ('名称', 90),
        ('价格', 65),
        ('涨跌幅', 65),
        ('涨跌额', 65),
        ('成交量', 80),
        ('成交额', 80),
        ('人气值', 65)
    ],
    'soaring': [
        ('排名', 45),
        ('代码', 65),
        ('名称', 90),
        ('价格', 65),
        ('涨跌幅', 65),
        ('涨跌额', 65),
        ('成交量', 80),
        ('成交额', 80),
        ('换手率', 65)
    ]
}

# 颜色配置
COLORS = {
    'rise': '#e53e3e',      # 上涨红色 (更现代的红色)
    'fall': '#38a169',      # 下跌绿色 (更现代的绿色)
    'equal': '#718096',     # 平盘灰色
    'background': '#ffffff', # 背景白色
    'text': '#2d3748',      # 文字颜色
    'rise_bg': '#fed7d7',   # 上涨背景色
    'fall_bg': '#c6f6d5',   # 下跌背景色
    'border': '#e2e8f0'     # 边框颜色
}
