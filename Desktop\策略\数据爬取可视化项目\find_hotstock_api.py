"""
寻找真正的热股榜API
"""
import requests
import json
import time
from datetime import datetime

def test_possible_apis():
    """测试可能的API接口"""
    print("=" * 60)
    print("寻找真正的热股榜API")
    print("=" * 60)
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'https://guba.eastmoney.com/',
        'X-Requested-With': 'XMLHttpRequest'
    }
    
    # 基于您展示的真实数据，尝试这些API
    test_apis = [
        # 股吧热股榜相关API
        {
            'name': '股吧热股榜API-1',
            'url': 'https://guba.eastmoney.com/api/rank/stock',
            'method': 'GET'
        },
        {
            'name': '股吧热股榜API-2', 
            'url': 'https://guba.eastmoney.com/rank/api/stock',
            'method': 'GET'
        },
        {
            'name': '股吧热股榜API-3',
            'url': 'https://guba.eastmoney.com/api/stock/rank',
            'method': 'GET'
        },
        
        # 数据中心相关API
        {
            'name': '数据中心热股榜',
            'url': 'https://datacenter-web.eastmoney.com/api/data/get',
            'method': 'GET',
            'params': {
                'type': 'RPT_HOTSTOCK_RANK',
                'sty': 'ALL',
                'p': '1',
                'ps': '50'
            }
        },
        
        # 推送服务API
        {
            'name': '推送服务热股榜',
            'url': 'https://push2.eastmoney.com/api/qt/rank/get',
            'method': 'GET',
            'params': {
                'cb': 'jQuery',
                'pn': '1',
                'pz': '50',
                'po': '1',
                'np': '1',
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': '2',
                'invt': '2',
                'fid': 'f164',  # 人气值字段
                'fs': 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23',
                'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f164'
            }
        },
        
        # 尝试移动端API
        {
            'name': '移动端热股榜',
            'url': 'https://emappdata.eastmoney.com/stockrank/getAllCurrentList',
            'method': 'GET',
            'params': {
                'appkey': 'eastmoney',
                'pageIndex': '0',
                'pageSize': '50',
                'type': '1'  # 热股榜类型
            }
        },
        
        # 尝试其他可能的接口
        {
            'name': 'WebAPI热股榜',
            'url': 'https://web.ifzq.gtimg.cn/appstock/app/hkstock/rank',
            'method': 'GET'
        },
        
        # 基于真实数据推测的API
        {
            'name': '推测的热股榜API',
            'url': 'https://push2.eastmoney.com/api/qt/clist/get',
            'method': 'GET',
            'params': {
                'cb': 'jQuery',
                'pn': '1',
                'pz': '100',
                'po': '1',
                'np': '1',
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': '2',
                'invt': '2',
                'fid': 'f3',  # 按涨跌幅排序
                'fs': 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23',
                'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25'
            }
        }
    ]
    
    session = requests.Session()
    session.headers.update(headers)
    
    for i, api in enumerate(test_apis, 1):
        print(f"\n{i}. 测试 {api['name']}")
        print(f"URL: {api['url']}")
        
        try:
            if api['method'] == 'GET':
                params = api.get('params', {})
                response = session.get(api['url'], params=params, timeout=15)
            else:
                response = session.post(api['url'], timeout=15)
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                print(f"响应长度: {len(content)}")
                
                # 检查是否包含我们要找的股票
                target_stocks = ['长城军工', '601606', '上纬新材', '688585']
                found_targets = [stock for stock in target_stocks if stock in content]
                
                if found_targets:
                    print(f"🎯 找到目标股票: {found_targets}")
                    
                    # 尝试解析数据
                    if content.strip().startswith('jQuery'):
                        # JSONP格式
                        try:
                            start = content.find('(') + 1
                            end = content.rfind(')')
                            json_text = content[start:end]
                            data = json.loads(json_text)
                            
                            if isinstance(data, dict) and 'data' in data:
                                if 'diff' in data['data']:
                                    stocks = data['data']['diff']
                                    print(f"✅ 成功解析到 {len(stocks)} 只股票")
                                    
                                    # 查找目标股票
                                    for stock in stocks[:10]:
                                        name = stock.get('f14', '')
                                        code = stock.get('f12', '')
                                        price = stock.get('f2', 0)
                                        change_pct = stock.get('f3', 0)
                                        
                                        if name in target_stocks or code in target_stocks:
                                            print(f"🎯 找到目标: {name} ({code}) {price} {change_pct}%")
                                        
                                        if name and code:
                                            print(f"  {name} ({code}) {price} {change_pct}%")
                                            
                        except Exception as e:
                            print(f"❌ JSONP解析失败: {e}")
                    
                    elif content.strip().startswith('{'):
                        # JSON格式
                        try:
                            data = json.loads(content)
                            print(f"✅ JSON解析成功")
                            print(f"数据键: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                        except Exception as e:
                            print(f"❌ JSON解析失败: {e}")
                
                else:
                    print("❌ 未找到目标股票")
                    
                # 显示响应的前200字符
                print(f"响应预览: {content[:200]}")
                
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
        
        # 避免请求过快
        time.sleep(1)

def check_current_hotstock():
    """检查当前我们程序获取的数据是否包含目标股票"""
    print("\n" + "=" * 60)
    print("检查当前程序数据")
    print("=" * 60)
    
    # 使用当前程序的API
    url = "https://push2.eastmoney.com/api/qt/clist/get"
    params = {
        'cb': 'jQuery',
        'pn': '1',
        'pz': '100',
        'po': '1',
        'np': '1',
        'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
        'fltt': '2',
        'invt': '2',
        'fid': 'f3',  # 按涨跌幅排序
        'fs': 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23',
        'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25'
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=15)
        
        if response.status_code == 200:
            content = response.text
            
            # 解析JSONP
            start = content.find('(') + 1
            end = content.rfind(')')
            json_text = content[start:end]
            data = json.loads(json_text)
            
            if 'data' in data and 'diff' in data['data']:
                stocks = data['data']['diff']
                print(f"当前获取到 {len(stocks)} 只股票")
                
                # 查找目标股票
                target_stocks = ['长城军工', '601606', '上纬新材', '688585']
                found = False
                
                for stock in stocks:
                    name = stock.get('f14', '')
                    code = stock.get('f12', '')
                    price = stock.get('f2', 0)
                    change_pct = stock.get('f3', 0)
                    
                    if name in target_stocks or code in target_stocks:
                        print(f"🎯 找到目标股票: {name} ({code}) {price} {change_pct}%")
                        found = True
                
                if not found:
                    print("❌ 当前数据中未找到目标股票")
                    print("前10只股票:")
                    for i, stock in enumerate(stocks[:10], 1):
                        name = stock.get('f14', '')
                        code = stock.get('f12', '')
                        price = stock.get('f2', 0)
                        change_pct = stock.get('f3', 0)
                        print(f"{i}. {name} ({code}) {price} {change_pct}%")
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    test_possible_apis()
    check_current_hotstock()
