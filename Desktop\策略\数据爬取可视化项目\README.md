# 东方财富实时榜单监控 - PyQt5版本

## 项目简介

这是一个基于PyQt5开发的桌面应用程序，用于实时监控东方财富网的人气榜和飙升榜数据。

### ✨ 主要特点

- 🖥️ **原生桌面应用**: 基于PyQt5，启动快速，界面流畅
- 📊 **双榜监控**: 同时显示人气榜和飙升榜TOP100数据
- 🔄 **实时更新**: 每5分钟自动更新数据，支持手动刷新
- 💾 **数据持久化**: SQLite数据库存储，支持历史数据查看
- 🎨 **现代化界面**: 美观的渐变色设计，支持涨跌颜色区分
- 🔍 **智能搜索**: 支持股票代码和名称搜索
- 📤 **数据导出**: 一键导出Excel格式数据
- 📈 **实时统计**: 显示涨跌股票数量统计

## 快速开始

### 方法一：使用批处理文件（推荐）

1. 双击 `启动程序.bat` 文件
2. 程序会自动检查并安装依赖
3. 启动成功后会显示主界面

### 方法二：命令行启动

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动程序
python main.py
```

### 方法三：测试数据获取

```bash
# 测试爬虫功能
python test_scraper.py

# 测试API接口
python test_data.py
```

## 界面预览

程序启动后会显示现代化的双栏界面：

- **左侧**: 📈 人气榜 TOP 100
- **右侧**: 🚀 飙升榜 TOP 100
- **顶部**: 工具栏（刷新、导出、设置、帮助）
- **中部**: 搜索栏和统计信息
- **底部**: 状态栏（更新时间、网络状态）

## 功能说明

### 1. 数据监控
- **人气榜**: 显示最受关注的100只股票
- **飙升榜**: 显示涨幅最大的100只股票
- **实时更新**: 每5分钟自动获取最新数据
- **手动刷新**: 点击"🔄 刷新"按钮立即更新

### 2. 数据展示
- **排名**: 1-100名排序
- **股票信息**: 代码、名称、价格、涨跌幅、涨跌额
- **成交数据**: 成交量、成交额
- **特色数据**: 人气值（人气榜）、换手率（飙升榜）
- **颜色区分**: 红色上涨、绿色下跌、灰色平盘

### 3. 交互功能
- **搜索**: 输入股票代码或名称快速定位
- **排序**: 点击列标题进行排序
- **详情**: 双击股票行查看详细信息
- **右键菜单**: 复制股票代码或名称

### 4. 数据导出
- **Excel导出**: 支持导出当前数据到Excel文件
- **格式**: 包含人气榜和飙升榜两个工作表
- **文件名**: 自动生成带时间戳的文件名

## 技术架构

### 核心技术栈
- **界面框架**: PyQt5
- **网络请求**: requests
- **数据解析**: json, beautifulsoup4
- **数据库**: SQLite
- **数据处理**: pandas
- **Excel导出**: openpyxl

### 项目结构
```
eastmoney_monitor/
├── main.py              # 程序入口
├── core/                # 核心模块
│   ├── config.py        # 配置文件
│   ├── scraper.py       # 数据爬虫
│   └── database.py      # 数据库操作
├── ui/                  # 界面模块
│   ├── main_window.py   # 主窗口
│   └── ranking_table.py # 排行榜表格
├── data/                # 数据目录
│   └── eastmoney_stocks.db  # SQLite数据库
├── test_data.py         # API测试脚本
├── test_scraper.py      # 爬虫测试脚本
├── requirements.txt     # 依赖包列表
├── 启动程序.bat         # Windows启动脚本
└── README.md           # 项目说明
```

## 配置说明

主要配置项在 `core/config.py` 中：

```python
# 更新间隔（毫秒）
UPDATE_INTERVAL = 5 * 60 * 1000  # 5分钟

# 窗口大小
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800

# 颜色配置
COLORS = {
    'rise': '#e53e3e',      # 上涨红色
    'fall': '#38a169',      # 下跌绿色
    'equal': '#718096',     # 平盘灰色
}
```

## 数据来源

数据来源于东方财富网公开API接口：
- **人气榜API**: 基于用户关注度排序
- **飙升榜API**: 基于涨跌幅排序
- **更新频率**: 实时数据，程序每5分钟获取一次

## 常见问题

### Q: 程序启动失败？
A: 请检查：
1. Python版本是否为3.7+
2. 是否正确安装了PyQt5
3. 网络连接是否正常

### Q: 数据显示为空？
A: 可能原因：
1. 网络连接问题
2. API接口临时不可用
3. 首次启动需要等待数据加载

### Q: 如何修改更新频率？
A: 在 `core/config.py` 中修改 `UPDATE_INTERVAL` 参数

### Q: 数据保存在哪里？
A: 数据保存在 `data/eastmoney_stocks.db` SQLite数据库文件中

## 注意事项

1. **合规使用**: 请遵守东方财富网的使用条款
2. **网络要求**: 需要稳定的网络连接
3. **系统要求**: Windows 7+ / macOS 10.12+ / Linux
4. **Python版本**: 需要Python 3.7或更高版本
5. **仅供参考**: 数据仅供参考，不构成投资建议

## 更新日志

### v1.0.0 (2024-01-05)
- ✅ 初始版本发布
- ✅ 支持人气榜和飙升榜数据获取
- ✅ 现代化PyQt5界面设计
- ✅ 实时数据更新和缓存
- ✅ 数据搜索和导出功能
- ✅ SQLite数据库存储

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途。

## 技术支持

如有问题或建议，请通过以下方式联系：
- 查看项目文档
- 运行测试脚本排查问题
- 检查网络连接和API可用性
