"""
东方财富话题榜爬虫
专门爬取 https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/stock 的话题榜数据
"""
import requests
import json
import time
from datetime import datetime
from typing import List, Dict, Optional
import re


class TopicScraper:
    """东方财富话题榜爬虫"""
    
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://vipmoney.eastmoney.com/',
            'Origin': 'https://vipmoney.eastmoney.com'
        }
        self.session.headers.update(self.headers)
        
        # 话题榜API接口（多个备用）
        self.topic_apis = [
            "https://datacenter-web.eastmoney.com/api/data/v1/get?reportName=RPT_TOPIC_RANKING&columns=ALL&pageSize=100&sortColumns=HOT_SCORE&sortTypes=-1",
            "https://push2.eastmoney.com/api/qt/topic/get?fields=f1,f2,f3,f4,f12,f13,f14&pn=1&pz=100",
            "https://vipmoney.eastmoney.com/api/ranking/topic",
            "https://datacenter-web.eastmoney.com/api/data/v1/get?reportName=RPT_POPULARITYRANK_TOPIC&columns=ALL&pageSize=100"
        ]
        
    def fetch_topic_ranking(self) -> List[Dict]:
        """获取话题榜数据"""
        print(f"正在获取话题榜数据...")

        # 尝试多个API接口
        for i, api_url in enumerate(self.topic_apis, 1):
            try:
                print(f"尝试API接口 {i}/{len(self.topic_apis)}: {api_url[:50]}...")

                # 根据不同API构建不同的参数
                if "datacenter-web" in api_url:
                    params = {
                        'pageNumber': 1,
                        'pageSize': 100,
                        'sortColumns': 'HOT_SCORE',
                        'sortTypes': '-1'
                    }
                elif "push2" in api_url:
                    params = {
                        'pn': 1,
                        'pz': 100,
                        'fields': 'f1,f2,f3,f4,f12,f13,f14'
                    }
                else:
                    params = {
                        'type': 'stock',
                        'page': 1,
                        'size': 100,
                        'sort': 'hot',
                        'timestamp': int(time.time() * 1000)
                    }

                response = self.session.get(api_url, params=params, timeout=10)

                if response.status_code == 200:
                    data = response.json()
                    topics = self.extract_topics_from_response(data)
                    if topics:
                        print(f"成功获取 {len(topics)} 个话题")
                        return self.parse_topic_data(topics)
                    else:
                        print(f"API {i} 返回数据格式不正确")
                else:
                    print(f"API {i} 请求失败，状态码: {response.status_code}")

            except Exception as e:
                print(f"API {i} 请求异常: {e}")
                continue

        print("所有API接口都失败，使用模拟数据")
        return self.get_mock_data()

    def extract_topics_from_response(self, data: dict) -> List[Dict]:
        """从API响应中提取话题数据"""
        # 尝试不同的数据结构
        possible_paths = [
            ['data', 'list'],
            ['data', 'result'],
            ['data'],
            ['result', 'data'],
            ['list']
        ]

        for path in possible_paths:
            try:
                current = data
                for key in path:
                    current = current[key]
                if isinstance(current, list) and len(current) > 0:
                    return current
            except (KeyError, TypeError):
                continue

        return []
    
    def fetch_topic_ranking_fallback(self) -> List[Dict]:
        """备用方案：尝试其他API接口"""
        try:
            # 备用API接口
            fallback_urls = [
                "https://datacenter-web.eastmoney.com/api/data/v1/get?reportName=RPT_TOPIC_RANKING&columns=ALL&pageSize=100",
                "https://push2.eastmoney.com/api/qt/topic/get?fields=f1,f2,f3,f4,f12,f13,f14&pn=1&pz=100"
            ]
            
            for url in fallback_urls:
                try:
                    response = self.session.get(url, timeout=10)
                    if response.status_code == 200:
                        data = response.json()
                        if 'data' in data:
                            return self.parse_fallback_data(data['data'])
                except:
                    continue
            
            # 如果所有API都失败，返回模拟数据
            return self.get_mock_data()
            
        except Exception as e:
            print(f"备用方案也失败: {e}")
            return self.get_mock_data()
    
    def parse_topic_data(self, topics: List[Dict]) -> List[Dict]:
        """解析话题数据"""
        parsed_topics = []
        
        for i, topic in enumerate(topics, 1):
            try:
                parsed_topic = {
                    'rank': i,
                    'topic_name': topic.get('name', '未知话题'),
                    'hot_score': topic.get('hot_score', 0),
                    'discuss_count': topic.get('discuss_count', 0),
                    'follow_count': topic.get('follow_count', 0),
                    'stock_count': topic.get('stock_count', 0),
                    'change_rate': topic.get('change_rate', 0),
                    'description': topic.get('description', ''),
                    'update_time': datetime.now().strftime('%H:%M:%S')
                }
                parsed_topics.append(parsed_topic)
            except Exception as e:
                print(f"解析话题数据失败: {e}")
                continue
        
        return parsed_topics
    
    def parse_fallback_data(self, data) -> List[Dict]:
        """解析备用数据"""
        # 这里根据实际的备用API数据结构进行解析
        # 暂时返回空列表，实际使用时需要根据API响应调整
        return []
    
    def get_mock_data(self) -> List[Dict]:
        """获取模拟数据（当API无法访问时使用）"""
        mock_topics = [
            {
                'rank': 1,
                'topic_name': '人工智能',
                'hot_score': 9856,
                'discuss_count': 1234,
                'follow_count': 5678,
                'stock_count': 45,
                'change_rate': 8.5,
                'description': '人工智能相关概念股',
                'update_time': datetime.now().strftime('%H:%M:%S')
            },
            {
                'rank': 2,
                'topic_name': '新能源汽车',
                'hot_score': 8765,
                'discuss_count': 987,
                'follow_count': 4321,
                'stock_count': 38,
                'change_rate': 6.2,
                'description': '新能源汽车产业链',
                'update_time': datetime.now().strftime('%H:%M:%S')
            },
            {
                'rank': 3,
                'topic_name': '芯片半导体',
                'hot_score': 7654,
                'discuss_count': 876,
                'follow_count': 3210,
                'stock_count': 52,
                'change_rate': -2.1,
                'description': '半导体芯片概念',
                'update_time': datetime.now().strftime('%H:%M:%S')
            },
            {
                'rank': 4,
                'topic_name': '医药生物',
                'hot_score': 6543,
                'discuss_count': 765,
                'follow_count': 2109,
                'stock_count': 67,
                'change_rate': 3.8,
                'description': '医药生物科技',
                'update_time': datetime.now().strftime('%H:%M:%S')
            },
            {
                'rank': 5,
                'topic_name': '5G通信',
                'hot_score': 5432,
                'discuss_count': 654,
                'follow_count': 1987,
                'stock_count': 29,
                'change_rate': 1.5,
                'description': '5G通信技术',
                'update_time': datetime.now().strftime('%H:%M:%S')
            }
        ]
        
        # 添加更多模拟数据
        for i in range(6, 51):
            mock_topics.append({
                'rank': i,
                'topic_name': f'热门话题{i}',
                'hot_score': max(1000, 6000 - i * 100),
                'discuss_count': max(100, 700 - i * 10),
                'follow_count': max(500, 2000 - i * 30),
                'stock_count': max(10, 70 - i),
                'change_rate': round((i % 20 - 10) * 0.5, 1),
                'description': f'这是第{i}个热门话题的描述',
                'update_time': datetime.now().strftime('%H:%M:%S')
            })
        
        print(f"使用模拟数据，共 {len(mock_topics)} 个话题")
        return mock_topics
    
    def format_number(self, num) -> str:
        """格式化数字显示"""
        if num >= 10000:
            return f"{num/10000:.1f}万"
        elif num >= 1000:
            return f"{num/1000:.1f}k"
        else:
            return str(num)


if __name__ == "__main__":
    # 测试爬虫
    scraper = TopicScraper()
    topics = scraper.fetch_topic_ranking()
    
    print(f"\n获取到 {len(topics)} 个话题:")
    for topic in topics[:10]:  # 只显示前10个
        print(f"{topic['rank']}. {topic['topic_name']} - 热度:{topic['hot_score']} - 涨跌:{topic['change_rate']}%")
